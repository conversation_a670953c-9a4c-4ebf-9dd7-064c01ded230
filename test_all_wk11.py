import os
import pandas as pd
import time

def quick_test_file(file_path):
    """Quick test to see if file can be opened"""
    try:
        # Just try to open the file for reading
        with open(file_path, 'rb') as f:
            f.read(1024)  # Read first 1KB
        return "OK - File accessible"
    except PermissionError:
        return "PERMISSION DENIED - File is locked/open"
    except Exception as e:
        return f"OTHER ERROR - {e}"

# Test all WK11 files
wk11_files = [
    r"Sainsbury\2023\Sainsbury - 2023 WK11.xlsx",
    r"Sainsbury\2024\Sainsbury - 2024 WK11.xlsx", 
    r"Sainsbury\2025\Sainsbury - 2025 WK11.xlsx"
]

print("Testing all WK11 files for accessibility:")
print("=" * 50)

for file_path in wk11_files:
    if os.path.exists(file_path):
        result = quick_test_file(file_path)
        size_kb = os.path.getsize(file_path) / 1024
        print(f"{file_path}")
        print(f"  Size: {size_kb:.1f} KB")
        print(f"  Status: {result}")
        print()
    else:
        print(f"{file_path} - FILE NOT FOUND")
        print()

# Also test a few other files for comparison
print("\nTesting other files for comparison:")
print("=" * 50)

other_files = [
    r"Sainsbury\2023\Sainsbury - 2023 WK10.xlsx",
    r"Sainsbury\2023\Sainsbury - 2023 WK12.xlsx",
]

for file_path in other_files:
    if os.path.exists(file_path):
        result = quick_test_file(file_path)
        size_kb = os.path.getsize(file_path) / 1024
        print(f"{file_path}")
        print(f"  Size: {size_kb:.1f} KB") 
        print(f"  Status: {result}")
        print()
