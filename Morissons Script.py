import os
import pandas as pd
import glob
import time
from datetime import datetime
try:
    import xlwings as xw  # Used as a fallback engine when openpyxl fails
except ImportError:
    xw = None  # Script will still run; but fallback will be unavailable

# Helper function to try multiple engines when reading the Excel files
def read_sales_file(file):
    """Use xlwings as primary engine since openpyxl fails on stylesheet issues."""
    file_start_time = time.time()
    print(f"    [DEBUG] Starting to read file: {os.path.basename(file)}")

    # Primary attempt: xlwings (handles corrupted stylesheets well)
    try:
        print(f"    [DEBUG] Using xlwings engine...")
        if xw is None:
            raise ImportError("xlwings is not installed - pip install xlwings to enable processing")
        app = xw.App(visible=False, add_book=False)
        wb = app.books.open(os.path.abspath(file))
        sht = wb.sheets[0]
        data = sht.used_range.value  # list of lists
        wb.close()
        app.quit()

        # Convert to DataFrame while replicating skiprows and usecols behaviour
        df = pd.DataFrame(data)
        df = df.iloc[8:, :5]  # skip first 8 rows, keep first 5 columns
        df.columns = df.iloc[0]  # first row after skip becomes header
        df = df[1:]  # drop header row from data
        print(f"    [DEBUG] xlwings successful - loaded {len(df)} rows")
    except Exception as xlwings_err:
        print(f"    [DEBUG] xlwings failed: {xlwings_err}")
        # Fallback attempt: pandas + openpyxl (in case xlwings has issues)
        try:
            print(f"    [DEBUG] Attempting openpyxl fallback...")
            # Data starts from row 9 (0-indexed row 8), so we skip 8 rows and use row 9 as header
            df = pd.read_excel(file, skiprows=8, usecols=range(5), dtype=str, engine="openpyxl")
            print(f"    [DEBUG] openpyxl fallback successful - loaded {len(df)} rows")
        except Exception as openpyxl_err:
            print(f"    [DEBUG] Both engines failed for {file}: xlwings error: {xlwings_err}, openpyxl error: {openpyxl_err}")
            return None

    print(f"    [DEBUG] Starting data cleaning...")
    # Common cleaning logic (shared with the original script)
    df = df.dropna(how='all')
    print(f"    [DEBUG] After dropping empty rows: {len(df)} rows")

    # Ensure we have the expected column names (handle variations)
    if len(df.columns) >= 5:
        df.columns = ['Periods', 'Item_Code', 'Store_Code', 'Sales_Value', 'Sales_Units']

    # Remove any rows that might be headers or empty
    df = df[df['Periods'] != 'Periods']  # Remove header rows that might have slipped through
    df = df.dropna(subset=['Periods'])  # Remove rows with no period data
    print(f"    [DEBUG] After cleaning headers/periods: {len(df)} rows")

    print(f"    [DEBUG] Processing date extraction...")
    # Extract week ending date from Periods column
    df['Week_Ending'] = pd.to_datetime(
        df['Periods'].str.extract(r'w/e\s+(\d{2}/\d{2}/\d{2})')[0], dayfirst=True
    )
    df['Week_Ending'] = df['Week_Ending'].dt.strftime('%m/%d/%y')

    file_end_time = time.time()
    file_duration = file_end_time - file_start_time
    print(f"    [DEBUG] File processing completed in {file_duration:.2f} seconds")
    return df


# Get folder path from user
folder_path = r'Sainsbury'
year=[2023,2024,2025]

print(f"[MAIN] Script started at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
script_start_time = time.time()

all_data = []
total_files_processed = 0
total_files_found = 0

for i in year:
    year_start_time = time.time()
    file_pattern = folder_path+f"\\{i}"+"\\*.xlsx"
    file_list = sorted(glob.glob(file_pattern))
    total_files_found += len(file_list)
    print(f"\n[YEAR {i}] Processing year {i}: Found {len(file_list)} files")
    print(f"[YEAR {i}] File pattern used: {file_pattern}")

    for idx, file in enumerate(file_list, 1):
        file_start_time = time.time()
        print(f"\n[FILE {idx}/{len(file_list)}] Processing: {os.path.basename(file)}")
        print(f"[FILE {idx}/{len(file_list)}] Full path: {file}")
        print(f"[FILE {idx}/{len(file_list)}] File size: {os.path.getsize(file) / 1024:.1f} KB")

        try:
            df = read_sales_file(file)
            if df is not None:
                all_data.append(df)
                total_files_processed += 1
                file_end_time = time.time()
                file_duration = file_end_time - file_start_time
                print(f"[FILE {idx}/{len(file_list)}] ✓ Successfully processed: {len(df)} rows in {file_duration:.2f} seconds")
                print(f"[PROGRESS] Total files processed so far: {total_files_processed}/{total_files_found}")
            else:
                print(f"[FILE {idx}/{len(file_list)}] ✗ Could not process {file}")
        except Exception as e:
            print(f"[FILE {idx}/{len(file_list)}] ✗ Error processing {file}: {e}")
            import traceback
            print(f"[ERROR] Full traceback:\n{traceback.format_exc()}")

    year_end_time = time.time()
    year_duration = year_end_time - year_start_time
    print(f"\n[YEAR {i}] Completed year {i} in {year_duration:.2f} seconds")

print(f"\n[MAIN] File processing phase completed. Starting data combination...")
combination_start_time = time.time()

if all_data:
    print(f"[MAIN] Combining {len(all_data)} DataFrames...")
    combined_df = pd.concat(all_data, ignore_index=True)
    print(f"[MAIN] Combined DataFrame has {len(combined_df)} total rows")

    # Columns are already properly named in the read_sales_file function
    # Final column order: Periods, Item_Code, Store_Code, Sales_Value, Sales_Units, Week_Ending
    # Save as CSV in the same folder
    print(f"[MAIN] Saving to CSV...")
    output_csv = os.path.join("Sainsbury.csv")
    combined_df.to_csv(output_csv, index=False)

    combination_end_time = time.time()
    combination_duration = combination_end_time - combination_start_time
    script_end_time = time.time()
    total_script_duration = script_end_time - script_start_time

    print(f"\n[MAIN] ✓ All done! Master CSV saved as: {output_csv}")
    print(f"[MAIN] Data combination took: {combination_duration:.2f} seconds")
    print(f"[MAIN] Total script execution time: {total_script_duration:.2f} seconds")
    print(f"[MAIN] Successfully processed {total_files_processed}/{total_files_found} files")
    print(f"[MAIN] Script completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
else:
    print("[MAIN] No data found to combine!")