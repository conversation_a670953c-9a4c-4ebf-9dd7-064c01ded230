import os
import pandas as pd
import time
import traceback
try:
    import xlwings as xw
except ImportError:
    xw = None

def test_read_file(file_path):
    """Test reading a specific file with detailed error reporting"""
    print(f"Testing file: {file_path}")
    print(f"File exists: {os.path.exists(file_path)}")
    print(f"File size: {os.path.getsize(file_path) / 1024:.1f} KB")
    
    # Test 1: Try openpyxl
    print("\n=== Testing openpyxl ===")
    try:
        start_time = time.time()
        df = pd.read_excel(file_path, skiprows=8, usecols=range(5), dtype=str, engine="openpyxl")
        end_time = time.time()
        print(f"SUCCESS: openpyxl loaded {len(df)} rows in {end_time - start_time:.2f} seconds")
        print(f"Columns: {list(df.columns)}")
        print(f"First few rows:\n{df.head()}")
        return df
    except Exception as e:
        print(f"FAILED: openpyxl error: {e}")
        print(f"Full traceback:\n{traceback.format_exc()}")
    
    # Test 2: Try xlwings if available
    if xw is not None:
        print("\n=== Testing xlwings ===")
        try:
            start_time = time.time()
            app = xw.App(visible=False, add_book=False)
            wb = app.books.open(os.path.abspath(file_path))
            sht = wb.sheets[0]
            data = sht.used_range.value
            wb.close()
            app.quit()
            
            df = pd.DataFrame(data)
            df = df.iloc[8:, :5]
            df.columns = df.iloc[0]
            df = df[1:]
            end_time = time.time()
            print(f"SUCCESS: xlwings loaded {len(df)} rows in {end_time - start_time:.2f} seconds")
            print(f"Columns: {list(df.columns)}")
            print(f"First few rows:\n{df.head()}")
            return df
        except Exception as e:
            print(f"FAILED: xlwings error: {e}")
            print(f"Full traceback:\n{traceback.format_exc()}")
    else:
        print("\n=== xlwings not available ===")
    
    # Test 3: Try different openpyxl parameters
    print("\n=== Testing openpyxl with different parameters ===")
    try:
        start_time = time.time()
        # Try without dtype=str
        df = pd.read_excel(file_path, skiprows=8, usecols=range(5), engine="openpyxl")
        end_time = time.time()
        print(f"SUCCESS: openpyxl (no dtype) loaded {len(df)} rows in {end_time - start_time:.2f} seconds")
        return df
    except Exception as e:
        print(f"FAILED: openpyxl (no dtype) error: {e}")
    
    # Test 4: Try reading without any parameters
    print("\n=== Testing basic read ===")
    try:
        start_time = time.time()
        df = pd.read_excel(file_path, engine="openpyxl")
        end_time = time.time()
        print(f"SUCCESS: basic read loaded {len(df)} rows in {end_time - start_time:.2f} seconds")
        print(f"Shape: {df.shape}")
        return df
    except Exception as e:
        print(f"FAILED: basic read error: {e}")
    
    return None

# Test the problematic WK11 file
test_file = r"Sainsbury\2023\Sainsbury - 2023 WK11.xlsx"
result = test_read_file(test_file)

if result is not None:
    print(f"\n=== Final result ===")
    print(f"Successfully read file with {len(result)} rows")
else:
    print(f"\n=== All methods failed ===")
